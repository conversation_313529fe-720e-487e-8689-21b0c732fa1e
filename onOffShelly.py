import tkinter as tk
from tkinter import messagebox
import requests
from requests.auth import HTTPBasicAuth

# Configuración del dispositivo Shelly
SHELLY_IP = "************"  # Reemplaza con la IP de tu Shelly Pro 4PM
USERNAME = "admin"           # Reemplaza con tu nombre de usuario
PASSWORD = "tu_clave"        # Reemplaza con tu contraseña

# Función para controlar las salidas
def controlar_salida(salida, estado):
    url = f"http://{SHELLY_IP}/rpc/Switch.Set"
    payload = {
        "id": salida,
        "on": estado
    }
    try:
        response = requests.post(url, json=payload, auth=HTTPBasicAuth(USERNAME, PASSWORD), timeout=5)
        if response.status_code == 200:
            estado_str = "encendida" if estado else "apagada"
            print(f"Salida {salida + 1} {estado_str}.")
        else:
            messagebox.showerror("Error", f"Error al controlar la salida {salida + 1}: {response.text}")
    except requests.exceptions.RequestException as e:
        messagebox.showerror("Error de conexión", f"No se pudo conectar al dispositivo: {e}")

# Crear la interfaz gráfica
ventana = tk.Tk()
ventana.title("Control de Shelly Pro 4PM")

for i in range(4):
    frame = tk.Frame(ventana)
    frame.pack(pady=5)

    etiqueta = tk.Label(frame, text=f"Salida {i + 1}")
    etiqueta.pack(side=tk.LEFT, padx=5)

    boton_encender = tk.Button(frame, text="Encender", command=lambda i=i: controlar_salida(i, True))
    boton_encender.pack(side=tk.LEFT, padx=5)

    boton_apagar = tk.Button(frame, text="Apagar", command=lambda i=i: controlar_salida(i, False))
    boton_apagar.pack(side=tk.LEFT, padx=5)

ventana.mainloop()
