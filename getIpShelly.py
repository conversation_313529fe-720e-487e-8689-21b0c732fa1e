from zeroconf import ServiceBrowser, Zeroconf
from rich.console import Console
from rich.table import Table
import time

class ShellyListener:
    def __init__(self):
        self.devices = []

    def add_service(self, zeroconf, type, name):
        info = zeroconf.get_service_info(type, name)
        if info:
            ip_address = ".".join(str(b) for b in info.addresses[0])
            device = {
                "name": name,
                "ip": ip_address,
                "port": info.port,
                "properties": info.properties
            }
            self.devices.append(device)

def discover_shelly_devices(timeout=5):
    zeroconf = Zeroconf()
    listener = ShellyListener()
    browser = ServiceBrowser(zeroconf, "_shelly._tcp.local.", listener)
    time.sleep(timeout)
    zeroconf.close()
    return listener.devices

def display_devices(devices):
    console = Console()
    table = Table(title="Dispositivos Shelly Descubiertos")

    table.add_column("Nombre", style="cyan")
    table.add_column("Dirección IP", style="green")
    table.add_column("Puerto", style="magenta")

    for device in devices:
        table.add_row(device["name"], device["ip"], str(device["port"]))

    console.print(table)

if __name__ == "__main__":
    devices = discover_shelly_devices()
    if devices:
        display_devices(devices)
    else:
        print("No se encontraron dispositivos Shelly en la red.")
Ñ