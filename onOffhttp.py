import requests

# Configuración Shelly
RELAY_ID = "083af2bf3904"  # ID del dispositivo (puedes cambiarlo)
RELAY_NAME = "Módulo Agua"  # Nombre personalizado
AUTH_KEY = "MjI5YWJmdWlk7657C240D5D844EFBE06DD2DA38027F00BCF35AEE88363118E917E53D1CBFF06EF64FC4B899A5614"
SHELLY_CLOUD_URL = "https://shelly-98-eu.shelly.cloud/relay/control"

# Configuración WhatsApp (TextMeBot)
TEXTMEBOT_API_KEY = "T7Rt3ER6b9RX"
WHATSAPP_GROUP_ID = "<EMAIL>"

def toggle_shelly_relay(state: str):
    """
    Enciende o apaga el relay especificado y envía un mensaje por WhatsApp.

    :param state: 'on' o 'off'
    """
    if state not in ["on", "off"]:
        raise ValueError("El estado debe ser 'on' o 'off'.")

    payload = {
        "id": RELAY_ID,
        "auth_key": AUTH_KEY,
        "turn": state
    }

    try:
        response = requests.post(SHELLY_CLOUD_URL, data=payload)
        response.raise_for_status()
        print(f"✅ Relay '{RELAY_NAME}' cambiado a: {state}")
        send_whatsapp_message(RELAY_NAME, state)
    except requests.RequestException as e:
        print(f"❌ Error al controlar el relay: {e}")

def send_whatsapp_message(device_name: str, action: str):
    """
    Envía un mensaje de estado del relay al grupo de WhatsApp.

    :param device_name: Nombre descriptivo del relay
    :param action: 'on' o 'off'
    """
    action_text = "ENCENDIDO 🔛" if action == "on" else "APAGADO 🔴"
    message = f"📢 Cambio de estado en dispositivo\n🖥️ {device_name}\n⚙️ Estado: {action_text}"

    url = f"https://api.textmebot.com/send.php?recipient={WHATSAPP_GROUP_ID}&apikey={TEXTMEBOT_API_KEY}&text={message}"
    try:
        response = requests.get(url)
        if response.status_code == 200:
            print("📩 Mensaje enviado al grupo de WhatsApp.")
        else:
            print(f"⚠️ Error enviando mensaje al grupo. Código: {response.status_code}")
    except Exception as e:
        print(f"❌ Error en la solicitud al API de WhatsApp: {e}")

# 🔄 Prueba de encendido/apagado
if _name_ == "_main_":
    toggle_shelly_relay("off")   # Cambiar a "off" para apagar